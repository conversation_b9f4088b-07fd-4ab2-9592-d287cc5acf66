import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DocumentFolder } from '../entities/document-folder.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository cho entity DocumentFolder với tenant isolation
 */
@Injectable()
export class DocumentFolderRepository {
  private readonly logger = new Logger(DocumentFolderRepository.name);

  constructor(
    @InjectRepository(DocumentFolder)
    private readonly repository: Repository<DocumentFolder>,
  ) {}

  /**
   * Tạo thư mục mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu thư mục
   * @returns Thư mục đã tạo
   */
  async create(tenantId: number, data: Partial<DocumentFolder>): Promise<DocumentFolder> {
    const folder = this.repository.create({ 
      ...data, 
      tenantId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    return this.repository.save(folder);
  }

  /**
   * Tìm tất cả thư mục với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param options Tùy chọn truy vấn
   * @returns Danh sách thư mục đã phân trang
   */
  async findAll(
    tenantId: number, 
    options: {
      page?: number;
      limit?: number;
      search?: string;
      parentId?: number | null;
      isActive?: boolean;
      sortBy?: string;
      sortDirection?: 'ASC' | 'DESC';
    } = {}
  ): Promise<PaginatedResult<DocumentFolder>> {
    const {
      page = 1,
      limit = 10,
      search,
      parentId,
      isActive,
      sortBy = 'name',
      sortDirection = 'ASC',
    } = options;

    const queryBuilder = this.repository.createQueryBuilder('folder');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('folder.tenantId = :tenantId', { tenantId });

    // Áp dụng bộ lọc parentId nếu được cung cấp
    if (parentId !== undefined) {
      if (parentId === null) {
        queryBuilder.andWhere('folder.parentId IS NULL');
      } else {
        queryBuilder.andWhere('folder.parentId = :parentId', { parentId });
      }
    }

    // Áp dụng bộ lọc isActive nếu được cung cấp
    if (isActive !== undefined) {
      queryBuilder.andWhere('folder.isActive = :isActive', { isActive });
    }

    // Áp dụng tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere(
        '(folder.name ILIKE :search OR folder.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Sắp xếp
    queryBuilder.orderBy(`folder.${sortBy}`, sortDirection);

    // Phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Tìm thư mục theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID thư mục
   * @returns Thư mục hoặc null
   */
  async findById(tenantId: number, id: number): Promise<DocumentFolder | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Tìm thư mục theo path
   * @param tenantId ID tenant (required for tenant isolation)
   * @param path Đường dẫn thư mục
   * @returns Thư mục hoặc null
   */
  async findByPath(tenantId: number, path: string): Promise<DocumentFolder | null> {
    return this.repository.findOne({
      where: { path, tenantId },
    });
  }

  /**
   * Tìm thư mục con của một thư mục cha
   * @param tenantId ID tenant (required for tenant isolation)
   * @param parentId ID thư mục cha
   * @returns Danh sách thư mục con
   */
  async findChildren(tenantId: number, parentId: number): Promise<DocumentFolder[]> {
    return this.repository.find({
      where: { parentId, tenantId, isActive: true },
      order: { sortOrder: 'ASC', name: 'ASC' },
    });
  }

  /**
   * Tìm tất cả thư mục gốc (không có parent)
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Danh sách thư mục gốc
   */
  async findRootFolders(tenantId: number): Promise<DocumentFolder[]> {
    return this.repository.find({
      where: { parentId: null, tenantId, isActive: true },
      order: { sortOrder: 'ASC', name: 'ASC' },
    });
  }

  /**
   * Cập nhật thư mục
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID thư mục
   * @param data Dữ liệu cập nhật
   * @returns Thư mục đã cập nhật hoặc null
   */
  async update(
    tenantId: number, 
    id: number, 
    data: Partial<DocumentFolder>
  ): Promise<DocumentFolder | null> {
    const folder = await this.findById(tenantId, id);
    if (!folder) return null;

    Object.assign(folder, data, { updatedAt: Date.now() });
    return this.repository.save(folder);
  }

  /**
   * Xóa thư mục (soft delete)
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID thư mục
   * @returns True nếu xóa thành công
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.update(
      { id, tenantId },
      { isActive: false, updatedAt: Date.now() }
    );
    return result.affected > 0;
  }

  /**
   * Kiểm tra thư mục có tồn tại không
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID thư mục
   * @returns True nếu tồn tại
   */
  async exists(tenantId: number, id: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { id, tenantId },
    });
    return count > 0;
  }

  /**
   * Đếm số thư mục con
   * @param tenantId ID tenant (required for tenant isolation)
   * @param parentId ID thư mục cha
   * @returns Số lượng thư mục con
   */
  async countChildren(tenantId: number, parentId: number): Promise<number> {
    return this.repository.count({
      where: { parentId, tenantId, isActive: true },
    });
  }
}
