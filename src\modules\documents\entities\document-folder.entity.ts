import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '@/modules/auth/entities/user.entity';

@Entity('document_folders')
@Index(['name', 'parentId', 'tenantId'], { unique: true })
@Index(['tenantId'])
@Index(['parentId'])
@Index(['path'])
@Index(['level'])
@Index(['isActive', 'tenantId'])
export class DocumentFolder {
  /**
   * Primary key
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Folder name
   */
  @Column({ name: 'name', type: 'varchar', length: 255 })
  name: string;

  /**
   * Folder description
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * Parent folder ID for hierarchical structure
   */
  @Column({ name: 'parent_id', type: 'integer', nullable: true })
  parentId: number | null;

  /**
   * Auto-calculated hierarchical path
   */
  @Column({ name: 'path', type: 'varchar', length: 1000 })
  path: string;

  /**
   * Depth level in hierarchy (0 = root level)
   */
  @Column({ name: 'level', type: 'integer', default: 0 })
  level: number;

  /**
   * Sort order within the same level
   */
  @Column({ name: 'sort_order', type: 'integer', default: 0 })
  sortOrder: number;

  /**
   * Whether the folder is active
   */
  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  /**
   * Additional folder metadata
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any> | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID of the user who created this folder
   */
  @Column({ name: 'created_by', type: 'integer' })
  createdBy: number;

  /**
   * ID of the user who last updated this folder
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * Tenant ID for multi-tenancy isolation
   */
  @Column({ name: 'tenant_id', type: 'integer' })
  tenantId: number;

  // =====================================================
  // RELATIONSHIPS
  // =====================================================

  /**
   * Parent folder relationship
   */
  @ManyToOne(() => DocumentFolder, (folder) => folder.children, {
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'parent_id' })
  parent: DocumentFolder | null;

  /**
   * Child folders relationship
   */
  @OneToMany(() => DocumentFolder, (folder) => folder.parent)
  children: DocumentFolder[];

  /**
   * User who created this folder
   */
  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  /**
   * User who last updated this folder
   */
  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updater: User | null;

  // =====================================================
  // COMPUTED PROPERTIES
  // =====================================================

  /**
   * Get the full folder path as an array
   */
  get pathArray(): string[] {
    return this.path.split('/').filter(Boolean);
  }

  /**
   * Check if this is a root folder
   */
  get isRoot(): boolean {
    return this.level === 0 && this.parentId === null;
  }
}
