import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Document } from '../entities/document.entity';
import { DocumentType, ProcessingStatus } from '../enums';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository cho entity Document với tenant isolation
 */
@Injectable()
export class DocumentRepository {
  private readonly logger = new Logger(DocumentRepository.name);

  constructor(
    @InjectRepository(Document)
    private readonly repository: Repository<Document>,
  ) {}

  /**
   * Tạo tài liệu mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu tài liệu
   * @returns Tài liệu đã tạo
   */
  async create(tenantId: number, data: Partial<Document>): Promise<Document> {
    const document = this.repository.create({ 
      ...data, 
      tenantId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    return this.repository.save(document);
  }

  /**
   * Tìm tất cả tài liệu với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param options Tùy chọn truy vấn
   * @returns Danh sách tài liệu đã phân trang
   */
  async findAll(
    tenantId: number, 
    options: {
      page?: number;
      limit?: number;
      search?: string;
      documentType?: DocumentType;
      folderId?: number;
      isActive?: boolean;
      isPublic?: boolean;
      processingStatus?: ProcessingStatus;
      tags?: string[];
      sortBy?: string;
      sortDirection?: 'ASC' | 'DESC';
    } = {}
  ): Promise<PaginatedResult<Document>> {
    const {
      page = 1,
      limit = 10,
      search,
      documentType,
      folderId,
      isActive,
      isPublic,
      processingStatus,
      tags,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = options;

    const queryBuilder = this.repository.createQueryBuilder('document');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('document.tenantId = :tenantId', { tenantId });

    // Áp dụng bộ lọc documentType nếu được cung cấp
    if (documentType) {
      queryBuilder.andWhere('document.documentType = :documentType', { documentType });
    }

    // Áp dụng bộ lọc folderId nếu được cung cấp
    if (folderId !== undefined) {
      if (folderId === null) {
        queryBuilder.andWhere('document.folderId IS NULL');
      } else {
        queryBuilder.andWhere('document.folderId = :folderId', { folderId });
      }
    }

    // Áp dụng bộ lọc isActive nếu được cung cấp
    if (isActive !== undefined) {
      queryBuilder.andWhere('document.isActive = :isActive', { isActive });
    }

    // Áp dụng bộ lọc isPublic nếu được cung cấp
    if (isPublic !== undefined) {
      queryBuilder.andWhere('document.isPublic = :isPublic', { isPublic });
    }

    // Áp dụng bộ lọc processingStatus nếu được cung cấp
    if (processingStatus) {
      queryBuilder.andWhere('document.processingStatus = :processingStatus', { processingStatus });
    }

    // Áp dụng bộ lọc tags nếu được cung cấp
    if (tags && tags.length > 0) {
      queryBuilder.andWhere('document.tags && :tags', { tags });
    }

    // Áp dụng tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere(
        '(document.title ILIKE :search OR document.description ILIKE :search OR document.fileName ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Sắp xếp
    queryBuilder.orderBy(`document.${sortBy}`, sortDirection);

    // Phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Tìm tài liệu theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID tài liệu
   * @returns Tài liệu hoặc null
   */
  async findById(tenantId: number, id: number): Promise<Document | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Tìm tài liệu theo S3 key
   * @param tenantId ID tenant (required for tenant isolation)
   * @param s3Key Khóa S3
   * @returns Tài liệu hoặc null
   */
  async findByS3Key(tenantId: number, s3Key: string): Promise<Document | null> {
    return this.repository.findOne({
      where: { s3Key, tenantId },
    });
  }

  /**
   * Tìm tài liệu theo OpenAI file ID
   * @param openaiFileId ID file OpenAI
   * @returns Tài liệu hoặc null
   */
  async findByOpenAIFileId(openaiFileId: string): Promise<Document | null> {
    return this.repository.findOne({
      where: { openaiFileId },
    });
  }

  /**
   * Tìm tài liệu theo content hash
   * @param tenantId ID tenant (required for tenant isolation)
   * @param contentHash Hash nội dung
   * @returns Tài liệu hoặc null
   */
  async findByContentHash(tenantId: number, contentHash: string): Promise<Document | null> {
    return this.repository.findOne({
      where: { contentHash, tenantId },
    });
  }

  /**
   * Tìm tài liệu trong thư mục
   * @param tenantId ID tenant (required for tenant isolation)
   * @param folderId ID thư mục
   * @returns Danh sách tài liệu
   */
  async findByFolder(tenantId: number, folderId: number): Promise<Document[]> {
    return this.repository.find({
      where: { folderId, tenantId, isActive: true },
      order: { title: 'ASC' },
    });
  }

  /**
   * Tìm tài liệu theo trạng thái xử lý
   * @param processingStatus Trạng thái xử lý
   * @param limit Giới hạn số lượng
   * @returns Danh sách tài liệu
   */
  async findByProcessingStatus(
    processingStatus: ProcessingStatus, 
    limit?: number
  ): Promise<Document[]> {
    const queryBuilder = this.repository.createQueryBuilder('document');
    
    queryBuilder.where('document.processingStatus = :processingStatus', { processingStatus });
    queryBuilder.orderBy('document.createdAt', 'ASC');
    
    if (limit) {
      queryBuilder.take(limit);
    }
    
    return queryBuilder.getMany();
  }

  /**
   * Tìm kiếm full-text
   * @param tenantId ID tenant (required for tenant isolation)
   * @param searchText Văn bản tìm kiếm
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách tài liệu đã phân trang
   */
  async searchFullText(
    tenantId: number, 
    searchText: string,
    options: {
      page?: number;
      limit?: number;
      documentType?: DocumentType;
      folderId?: number;
    } = {}
  ): Promise<PaginatedResult<Document>> {
    const { page = 1, limit = 10, documentType, folderId } = options;

    const queryBuilder = this.repository.createQueryBuilder('document');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('document.tenantId = :tenantId', { tenantId });

    // Full-text search
    queryBuilder.andWhere(
      `to_tsvector('english', COALESCE(document.title, '') || ' ' || COALESCE(document.description, '') || ' ' || COALESCE(document.extractedText, '')) @@ plainto_tsquery('english', :searchText)`,
      { searchText }
    );

    // Áp dụng bộ lọc bổ sung
    if (documentType) {
      queryBuilder.andWhere('document.documentType = :documentType', { documentType });
    }

    if (folderId !== undefined) {
      if (folderId === null) {
        queryBuilder.andWhere('document.folderId IS NULL');
      } else {
        queryBuilder.andWhere('document.folderId = :folderId', { folderId });
      }
    }

    // Chỉ tài liệu active
    queryBuilder.andWhere('document.isActive = true');

    // Sắp xếp theo relevance
    queryBuilder.orderBy('document.createdAt', 'DESC');

    // Phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Cập nhật tài liệu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID tài liệu
   * @param data Dữ liệu cập nhật
   * @returns Tài liệu đã cập nhật hoặc null
   */
  async update(
    tenantId: number, 
    id: number, 
    data: Partial<Document>
  ): Promise<Document | null> {
    const document = await this.findById(tenantId, id);
    if (!document) return null;

    Object.assign(document, data, { updatedAt: Date.now() });
    return this.repository.save(document);
  }

  /**
   * Cập nhật trạng thái xử lý
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID tài liệu
   * @param processingStatus Trạng thái xử lý mới
   * @param processingError Lỗi xử lý (nếu có)
   * @returns Tài liệu đã cập nhật hoặc null
   */
  async updateProcessingStatus(
    tenantId: number, 
    id: number, 
    processingStatus: ProcessingStatus,
    processingError?: string
  ): Promise<Document | null> {
    return this.update(tenantId, id, {
      processingStatus,
      processingError,
      lastProcessedAt: Date.now(),
    });
  }

  /**
   * Xóa tài liệu (soft delete)
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID tài liệu
   * @returns True nếu xóa thành công
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.update(
      { id, tenantId },
      { isActive: false, updatedAt: Date.now() }
    );
    return result.affected > 0;
  }

  /**
   * Kiểm tra tài liệu có tồn tại không
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID tài liệu
   * @returns True nếu tồn tại
   */
  async exists(tenantId: number, id: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { id, tenantId },
    });
    return count > 0;
  }

  /**
   * Đếm tài liệu theo loại
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Thống kê theo loại tài liệu
   */
  async countByType(tenantId: number): Promise<Record<DocumentType, number>> {
    const result = await this.repository
      .createQueryBuilder('document')
      .select('document.documentType', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('document.tenantId = :tenantId', { tenantId })
      .andWhere('document.isActive = true')
      .groupBy('document.documentType')
      .getRawMany();

    const counts = {} as Record<DocumentType, number>;
    
    // Khởi tạo tất cả types với 0
    Object.values(DocumentType).forEach(type => {
      counts[type] = 0;
    });

    // Cập nhật với dữ liệu thực tế
    result.forEach(row => {
      counts[row.type as DocumentType] = parseInt(row.count);
    });

    return counts;
  }
}
