import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DocumentPermission } from '../entities/document-permission.entity';
import { PermissionLevel } from '../enums';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository cho entity DocumentPermission với tenant isolation
 */
@Injectable()
export class DocumentPermissionRepository {
  private readonly logger = new Logger(DocumentPermissionRepository.name);

  constructor(
    @InjectRepository(DocumentPermission)
    private readonly repository: Repository<DocumentPermission>,
  ) {}

  /**
   * Tạo quyền tài liệu mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu quyền
   * @returns Quyền đã tạo
   */
  async create(tenantId: number, data: Partial<DocumentPermission>): Promise<DocumentPermission> {
    const permission = this.repository.create({ 
      ...data, 
      tenantId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    return this.repository.save(permission);
  }

  /**
   * Tìm tất cả quyền với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param options Tùy chọn truy vấn
   * @returns Danh sách quyền đã phân trang
   */
  async findAll(
    tenantId: number, 
    options: {
      page?: number;
      limit?: number;
      documentId?: number;
      userId?: number;
      roleId?: number;
      departmentId?: number;
      permissionLevel?: PermissionLevel;
      isInherited?: boolean;
      sortBy?: string;
      sortDirection?: 'ASC' | 'DESC';
    } = {}
  ): Promise<PaginatedResult<DocumentPermission>> {
    const {
      page = 1,
      limit = 10,
      documentId,
      userId,
      roleId,
      departmentId,
      permissionLevel,
      isInherited,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = options;

    const queryBuilder = this.repository.createQueryBuilder('permission');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('permission.tenantId = :tenantId', { tenantId });

    // Áp dụng bộ lọc documentId nếu được cung cấp
    if (documentId) {
      queryBuilder.andWhere('permission.documentId = :documentId', { documentId });
    }

    // Áp dụng bộ lọc userId nếu được cung cấp
    if (userId) {
      queryBuilder.andWhere('permission.userId = :userId', { userId });
    }

    // Áp dụng bộ lọc roleId nếu được cung cấp
    if (roleId) {
      queryBuilder.andWhere('permission.roleId = :roleId', { roleId });
    }

    // Áp dụng bộ lọc departmentId nếu được cung cấp
    if (departmentId) {
      queryBuilder.andWhere('permission.departmentId = :departmentId', { departmentId });
    }

    // Áp dụng bộ lọc permissionLevel nếu được cung cấp
    if (permissionLevel) {
      queryBuilder.andWhere('permission.permissionLevel = :permissionLevel', { permissionLevel });
    }

    // Áp dụng bộ lọc isInherited nếu được cung cấp
    if (isInherited !== undefined) {
      queryBuilder.andWhere('permission.isInherited = :isInherited', { isInherited });
    }

    // Sắp xếp
    queryBuilder.orderBy(`permission.${sortBy}`, sortDirection);

    // Phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Tìm quyền theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID quyền
   * @returns Quyền hoặc null
   */
  async findById(tenantId: number, id: number): Promise<DocumentPermission | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Tìm quyền của user cho tài liệu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param documentId ID tài liệu
   * @param userId ID user
   * @returns Quyền hoặc null
   */
  async findUserPermission(
    tenantId: number, 
    documentId: number, 
    userId: number
  ): Promise<DocumentPermission | null> {
    return this.repository.findOne({
      where: { documentId, userId, tenantId },
    });
  }

  /**
   * Tìm quyền của role cho tài liệu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param documentId ID tài liệu
   * @param roleId ID role
   * @returns Quyền hoặc null
   */
  async findRolePermission(
    tenantId: number, 
    documentId: number, 
    roleId: number
  ): Promise<DocumentPermission | null> {
    return this.repository.findOne({
      where: { documentId, roleId, tenantId },
    });
  }

  /**
   * Tìm quyền của department cho tài liệu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param documentId ID tài liệu
   * @param departmentId ID department
   * @returns Quyền hoặc null
   */
  async findDepartmentPermission(
    tenantId: number, 
    documentId: number, 
    departmentId: number
  ): Promise<DocumentPermission | null> {
    return this.repository.findOne({
      where: { documentId, departmentId, tenantId },
    });
  }

  /**
   * Tìm tất cả quyền của tài liệu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param documentId ID tài liệu
   * @returns Danh sách quyền
   */
  async findByDocument(tenantId: number, documentId: number): Promise<DocumentPermission[]> {
    return this.repository.find({
      where: { documentId, tenantId },
      order: { permissionLevel: 'DESC', createdAt: 'ASC' },
    });
  }

  /**
   * Tìm tất cả quyền của user
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID user
   * @returns Danh sách quyền
   */
  async findByUser(tenantId: number, userId: number): Promise<DocumentPermission[]> {
    return this.repository.find({
      where: { userId, tenantId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm quyền hết hạn
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Danh sách quyền hết hạn
   */
  async findExpired(tenantId: number): Promise<DocumentPermission[]> {
    const now = Date.now();
    
    return this.repository
      .createQueryBuilder('permission')
      .where('permission.tenantId = :tenantId', { tenantId })
      .andWhere('permission.expiresAt IS NOT NULL')
      .andWhere('permission.expiresAt <= :now', { now })
      .orderBy('permission.expiresAt', 'ASC')
      .getMany();
  }

  /**
   * Tìm quyền sắp hết hạn
   * @param tenantId ID tenant (required for tenant isolation)
   * @param daysBeforeExpiration Số ngày trước khi hết hạn
   * @returns Danh sách quyền sắp hết hạn
   */
  async findExpiringSoon(
    tenantId: number, 
    daysBeforeExpiration: number = 7
  ): Promise<DocumentPermission[]> {
    const expirationThreshold = Date.now() + (daysBeforeExpiration * 24 * 60 * 60 * 1000);
    
    return this.repository
      .createQueryBuilder('permission')
      .where('permission.tenantId = :tenantId', { tenantId })
      .andWhere('permission.expiresAt IS NOT NULL')
      .andWhere('permission.expiresAt <= :threshold', { threshold: expirationThreshold })
      .andWhere('permission.expiresAt > :now', { now: Date.now() })
      .orderBy('permission.expiresAt', 'ASC')
      .getMany();
  }

  /**
   * Cập nhật quyền
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID quyền
   * @param data Dữ liệu cập nhật
   * @returns Quyền đã cập nhật hoặc null
   */
  async update(
    tenantId: number, 
    id: number, 
    data: Partial<DocumentPermission>
  ): Promise<DocumentPermission | null> {
    const permission = await this.findById(tenantId, id);
    if (!permission) return null;

    Object.assign(permission, data, { updatedAt: Date.now() });
    return this.repository.save(permission);
  }

  /**
   * Xóa quyền
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID quyền
   * @returns True nếu xóa thành công
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return result.affected > 0;
  }

  /**
   * Xóa tất cả quyền của tài liệu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param documentId ID tài liệu
   * @returns Số quyền đã xóa
   */
  async deleteByDocument(tenantId: number, documentId: number): Promise<number> {
    const result = await this.repository.delete({ documentId, tenantId });
    return result.affected || 0;
  }

  /**
   * Xóa quyền hết hạn
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Số quyền đã xóa
   */
  async deleteExpired(tenantId: number): Promise<number> {
    const expiredPermissions = await this.findExpired(tenantId);
    if (expiredPermissions.length === 0) return 0;

    const ids = expiredPermissions.map(p => p.id);
    const result = await this.repository.delete(ids);
    return result.affected || 0;
  }

  /**
   * Kiểm tra quyền có tồn tại không
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID quyền
   * @returns True nếu tồn tại
   */
  async exists(tenantId: number, id: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { id, tenantId },
    });
    return count > 0;
  }

  /**
   * Đếm quyền theo cấp độ
   * @param tenantId ID tenant (required for tenant isolation)
   * @param documentId ID tài liệu (tùy chọn)
   * @returns Thống kê theo cấp độ quyền
   */
  async countByLevel(
    tenantId: number, 
    documentId?: number
  ): Promise<Record<PermissionLevel, number>> {
    const queryBuilder = this.repository.createQueryBuilder('permission');
    
    queryBuilder
      .select('permission.permissionLevel', 'level')
      .addSelect('COUNT(*)', 'count')
      .where('permission.tenantId = :tenantId', { tenantId });

    if (documentId) {
      queryBuilder.andWhere('permission.documentId = :documentId', { documentId });
    }

    queryBuilder.groupBy('permission.permissionLevel');

    const result = await queryBuilder.getRawMany();

    const counts = {} as Record<PermissionLevel, number>;
    
    // Khởi tạo tất cả levels với 0
    Object.values(PermissionLevel).forEach(level => {
      counts[level] = 0;
    });

    // Cập nhật với dữ liệu thực tế
    result.forEach(row => {
      counts[row.level as PermissionLevel] = parseInt(row.count);
    });

    return counts;
  }
}
