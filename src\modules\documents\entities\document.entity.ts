import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '@/modules/auth/entities/user.entity';
import { DocumentFolder } from './document-folder.entity';
import { OpenAIVectorStore } from './openai-vector-store.entity';
import { DocumentType, ProcessingStatus } from '../enums';

@Entity('documents')
@Index(['tenantId'])
@Index(['folderId'])
@Index(['documentType', 'tenantId'])
@Index(['createdBy'])
@Index(['createdAt'])
@Index(['isActive', 'tenantId'])
@Index(['isPublic', 'tenantId'])
@Index(['processingStatus'])
@Index(['openaiFileId'])
@Index(['openaiVectorStoreId'])
@Index(['contentHash'])
@Index(['s3Key', 'tenantId'], { unique: true })
@Index(['openaiFileId'], { unique: true })
export class Document {
  /**
   * Primary key
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Document title
   */
  @Column({ name: 'title', type: 'varchar', length: 500 })
  title: string;

  /**
   * Document description
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * Document type
   */
  @Column({ name: 'document_type', type: 'varchar', length: 50 })
  documentType: DocumentType;

  /**
   * Original file name
   */
  @Column({ name: 'file_name', type: 'varchar', length: 255 })
  fileName: string;

  /**
   * File size in bytes
   */
  @Column({ name: 'file_size', type: 'bigint' })
  fileSize: number;

  /**
   * MIME type
   */
  @Column({ name: 'mime_type', type: 'varchar', length: 100 })
  mimeType: string;

  /**
   * Folder ID for organization
   */
  @Column({ name: 'folder_id', type: 'integer', nullable: true })
  folderId: number | null;

  // =====================================================
  // S3 STORAGE FIELDS
  // =====================================================

  /**
   * S3 bucket name
   */
  @Column({ name: 's3_bucket', type: 'varchar', length: 100, nullable: true })
  s3Bucket: string | null;

  /**
   * S3 object key
   */
  @Column({ name: 's3_key', type: 'varchar', length: 500 })
  s3Key: string;

  /**
   * S3 region
   */
  @Column({ name: 's3_region', type: 'varchar', length: 50, nullable: true })
  s3Region: string | null;

  /**
   * S3 ETag
   */
  @Column({ name: 's3_etag', type: 'varchar', length: 100, nullable: true })
  s3Etag: string | null;

  /**
   * S3 version ID
   */
  @Column({ name: 's3_version_id', type: 'varchar', length: 100, nullable: true })
  s3VersionId: string | null;

  // =====================================================
  // OPENAI INTEGRATION FIELDS
  // =====================================================

  /**
   * OpenAI Files API ID
   */
  @Column({ name: 'openai_file_id', type: 'varchar', length: 100, nullable: true })
  openaiFileId: string | null;

  /**
   * OpenAI Vector Store ID
   */
  @Column({ name: 'openai_vector_store_id', type: 'varchar', length: 100, nullable: true })
  openaiVectorStoreId: string | null;

  /**
   * OpenAI processing status
   */
  @Column({ name: 'processing_status', type: 'varchar', length: 50, default: ProcessingStatus.PENDING })
  processingStatus: ProcessingStatus;

  /**
   * Processing error message
   */
  @Column({ name: 'processing_error', type: 'text', nullable: true })
  processingError: string | null;

  /**
   * Last processing attempt timestamp
   */
  @Column({ name: 'last_processed_at', type: 'bigint', nullable: true })
  lastProcessedAt: number | null;

  /**
   * Number of processing retry attempts
   */
  @Column({ name: 'retry_count', type: 'integer', default: 0 })
  retryCount: number;

  // =====================================================
  // CONTENT METADATA FIELDS
  // =====================================================

  /**
   * SHA-256 hash for duplicate detection
   */
  @Column({ name: 'content_hash', type: 'varchar', length: 64, nullable: true })
  contentHash: string | null;

  /**
   * Number of pages (for PDF documents)
   */
  @Column({ name: 'page_count', type: 'integer', nullable: true })
  pageCount: number | null;

  /**
   * Word count
   */
  @Column({ name: 'word_count', type: 'integer', nullable: true })
  wordCount: number | null;

  /**
   * Document language
   */
  @Column({ name: 'language', type: 'varchar', length: 10, default: 'vi' })
  language: string;

  /**
   * Extracted text content for search
   */
  @Column({ name: 'extracted_text', type: 'text', nullable: true })
  extractedText: string | null;

  // =====================================================
  // STATUS AND VISIBILITY FIELDS
  // =====================================================

  /**
   * Whether the document is active
   */
  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  /**
   * Whether the document is public (bypasses permission checks)
   */
  @Column({ name: 'is_public', type: 'boolean', default: false })
  isPublic: boolean;

  /**
   * Document version
   */
  @Column({ name: 'version', type: 'integer', default: 1 })
  version: number;

  /**
   * Document tags
   */
  @Column({ name: 'tags', type: 'text', array: true, nullable: true })
  tags: string[] | null;

  /**
   * Additional document metadata
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any> | null;

  // =====================================================
  // AUDIT FIELDS
  // =====================================================

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID of the user who created this document
   */
  @Column({ name: 'created_by', type: 'integer' })
  createdBy: number;

  /**
   * ID of the user who last updated this document
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * Tenant ID for multi-tenancy isolation
   */
  @Column({ name: 'tenant_id', type: 'integer' })
  tenantId: number;

  // =====================================================
  // RELATIONSHIPS
  // =====================================================

  /**
   * Folder relationship
   */
  @ManyToOne(() => DocumentFolder, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'folder_id' })
  folder: DocumentFolder | null;

  /**
   * OpenAI Vector Store relationship
   */
  @ManyToOne(() => OpenAIVectorStore, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'openai_vector_store_id', referencedColumnName: 'vectorStoreId' })
  vectorStore: OpenAIVectorStore | null;

  /**
   * User who created this document
   */
  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  /**
   * User who last updated this document
   */
  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updater: User | null;

  // =====================================================
  // COMPUTED PROPERTIES
  // =====================================================

  /**
   * Get file size in MB
   */
  get fileSizeMB(): number {
    return Math.round(this.fileSize / (1024 * 1024) * 100) / 100;
  }

  /**
   * Check if document is processed successfully
   */
  get isProcessed(): boolean {
    return this.processingStatus === ProcessingStatus.COMPLETED;
  }

  /**
   * Check if document processing failed
   */
  get isProcessingFailed(): boolean {
    return this.processingStatus === ProcessingStatus.FAILED;
  }

  /**
   * Check if document can be retried
   */
  get canRetry(): boolean {
    return this.isProcessingFailed && this.retryCount < 3;
  }

  /**
   * Get file extension from filename
   */
  get fileExtension(): string {
    return this.fileName.split('.').pop()?.toLowerCase() || '';
  }

  /**
   * Check if document is a PDF
   */
  get isPDF(): boolean {
    return this.mimeType === 'application/pdf' || this.fileExtension === 'pdf';
  }

  /**
   * Check if document is a Word document
   */
  get isWordDocument(): boolean {
    return this.mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
           this.fileExtension === 'docx';
  }

  /**
   * Check if document is a text file
   */
  get isTextFile(): boolean {
    return this.mimeType.startsWith('text/') || ['txt', 'md'].includes(this.fileExtension);
  }
}
