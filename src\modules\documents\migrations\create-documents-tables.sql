-- =====================================================
-- Document Management Module - Database Schema
-- =====================================================
-- Created: 2025-01-03
-- Description: Complete database schema for document management with OpenAI integration and tenant isolation

-- Enable UUID extension if not exists
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. DOCUMENT FOLDERS TABLE
-- =====================================================
-- Hierarchical folder structure for organizing documents
DROP TABLE IF EXISTS document_folders CASCADE;
CREATE TABLE document_folders (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id INTEGER,
    path VARCHAR(1000) NOT NULL, -- Auto-calculated hierarchical path
    level INTEGER NOT NULL DEFAULT 0, -- Depth level in hierarchy
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    metadata JSONB, -- Additional folder metadata
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by INTEGER NOT NULL,
    updated_by INTEGER,
    tenant_id INTEGER NOT NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_document_folders_parent FOREIGN KEY (parent_id) REFERENCES document_folders(id) ON DELETE SET NULL,
    CONSTRAINT fk_document_folders_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_document_folders_updated_by FOREIGN KEY (updated_by) REFERENCES users(id),
    
    -- Unique constraints
    CONSTRAINT document_folders_name_parent_tenant_unique UNIQUE (name, parent_id, tenant_id)
);

-- =====================================================
-- 2. OPENAI VECTOR STORES TABLE  
-- =====================================================
-- Track OpenAI Vector Stores per tenant
DROP TABLE IF EXISTS openai_vector_stores CASCADE;
CREATE TABLE openai_vector_stores (
    id SERIAL PRIMARY KEY,
    vector_store_id VARCHAR(100) NOT NULL UNIQUE, -- OpenAI Vector Store ID
    name VARCHAR(255) NOT NULL,
    description TEXT,
    file_count INTEGER NOT NULL DEFAULT 0,
    usage_bytes BIGINT NOT NULL DEFAULT 0,
    status VARCHAR(50) NOT NULL DEFAULT 'active', -- active, expired, deleting
    expires_at BIGINT, -- OpenAI expiration timestamp
    last_synced_at BIGINT, -- Last sync with OpenAI
    metadata JSONB, -- OpenAI metadata
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    tenant_id INTEGER NOT NULL,
    
    -- Constraints
    CONSTRAINT openai_vector_stores_tenant_unique UNIQUE (tenant_id)
);

-- =====================================================
-- 3. DOCUMENTS TABLE
-- =====================================================
-- Main documents table with OpenAI integration
DROP TABLE IF EXISTS documents CASCADE;
CREATE TABLE documents (
    id SERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    document_type VARCHAR(50) NOT NULL, -- policy, procedure, guideline, manual, form, template, report, other
    file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    
    -- Folder organization
    folder_id INTEGER,
    
    -- S3 Storage
    s3_bucket VARCHAR(100),
    s3_key VARCHAR(500) NOT NULL,
    s3_region VARCHAR(50),
    s3_etag VARCHAR(100),
    s3_version_id VARCHAR(100),
    
    -- OpenAI Integration
    openai_file_id VARCHAR(100), -- OpenAI Files API ID
    openai_vector_store_id VARCHAR(100), -- Reference to vector store
    processing_status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, uploading_to_openai, adding_to_vector_store, completed, failed, retry
    processing_error TEXT, -- Error message if processing failed
    last_processed_at BIGINT, -- Last processing attempt
    retry_count INTEGER NOT NULL DEFAULT 0,
    
    -- Content metadata
    content_hash VARCHAR(64), -- SHA-256 hash for duplicate detection
    page_count INTEGER,
    word_count INTEGER,
    language VARCHAR(10) DEFAULT 'vi',
    extracted_text TEXT, -- Extracted text content for search
    
    -- Status and visibility
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_public BOOLEAN NOT NULL DEFAULT FALSE, -- Public documents don't need permissions
    version INTEGER NOT NULL DEFAULT 1,
    
    -- Additional metadata
    tags TEXT[], -- Array of tags
    metadata JSONB, -- Additional document metadata
    
    -- Audit fields
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by INTEGER NOT NULL,
    updated_by INTEGER,
    tenant_id INTEGER NOT NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_documents_folder FOREIGN KEY (folder_id) REFERENCES document_folders(id) ON DELETE SET NULL,
    CONSTRAINT fk_documents_vector_store FOREIGN KEY (openai_vector_store_id) REFERENCES openai_vector_stores(vector_store_id) ON DELETE SET NULL,
    CONSTRAINT fk_documents_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_documents_updated_by FOREIGN KEY (updated_by) REFERENCES users(id),
    
    -- Unique constraints
    CONSTRAINT documents_s3_key_tenant_unique UNIQUE (s3_key, tenant_id),
    CONSTRAINT documents_openai_file_unique UNIQUE (openai_file_id)
);

-- =====================================================
-- 4. DOCUMENT PERMISSIONS TABLE
-- =====================================================
-- Multi-level permissions (user, role, department)
DROP TABLE IF EXISTS document_permissions CASCADE;
CREATE TABLE document_permissions (
    id SERIAL PRIMARY KEY,
    document_id INTEGER NOT NULL,

    -- Permission target (one of these must be set)
    user_id INTEGER, -- User-specific permission
    role_id INTEGER, -- Role-based permission
    department_id INTEGER, -- Department-based permission

    -- Permission level
    permission_level VARCHAR(20) NOT NULL, -- read, write, admin

    -- Permission metadata
    granted_by INTEGER NOT NULL, -- User who granted this permission
    granted_at BIGINT NOT NULL,
    expires_at BIGINT, -- Optional expiration
    is_inherited BOOLEAN NOT NULL DEFAULT FALSE, -- Inherited from folder
    notes TEXT, -- Optional notes about permission

    -- Audit fields
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    tenant_id INTEGER NOT NULL,

    -- Foreign key constraints
    CONSTRAINT fk_document_permissions_document FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    CONSTRAINT fk_document_permissions_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_document_permissions_granted_by FOREIGN KEY (granted_by) REFERENCES users(id),

    -- Ensure only one permission target is set
    CONSTRAINT document_permissions_single_target CHECK (
        (user_id IS NOT NULL AND role_id IS NULL AND department_id IS NULL) OR
        (user_id IS NULL AND role_id IS NOT NULL AND department_id IS NULL) OR
        (user_id IS NULL AND role_id IS NULL AND department_id IS NOT NULL)
    ),

    -- Unique constraints to prevent duplicate permissions
    CONSTRAINT document_permissions_user_unique UNIQUE (document_id, user_id, tenant_id),
    CONSTRAINT document_permissions_role_unique UNIQUE (document_id, role_id, tenant_id),
    CONSTRAINT document_permissions_department_unique UNIQUE (document_id, department_id, tenant_id)
);

-- =====================================================
-- 5. DOCUMENT ACCESS LOGS TABLE
-- =====================================================
-- Track document access and RAG queries
DROP TABLE IF EXISTS document_access_logs CASCADE;
CREATE TABLE document_access_logs (
    id SERIAL PRIMARY KEY,
    document_id INTEGER,
    user_id INTEGER NOT NULL,

    -- Access type
    access_type VARCHAR(50) NOT NULL, -- view, download, search, rag_query, edit, delete

    -- RAG query specific fields
    query_text TEXT, -- Original query for RAG searches
    query_results JSONB, -- RAG search results
    query_confidence DECIMAL(3,2), -- AI confidence score

    -- Request metadata
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(100),

    -- Response metadata
    response_time_ms INTEGER, -- Response time in milliseconds
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,

    -- Additional metadata
    metadata JSONB,

    -- Audit fields
    created_at BIGINT NOT NULL,
    tenant_id INTEGER NOT NULL,

    -- Foreign key constraints
    CONSTRAINT fk_document_access_logs_document FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE SET NULL,
    CONSTRAINT fk_document_access_logs_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);



-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Document Folders Indexes
CREATE INDEX idx_document_folders_tenant_id ON document_folders(tenant_id);
CREATE INDEX idx_document_folders_parent_id ON document_folders(parent_id);
CREATE INDEX idx_document_folders_path ON document_folders(path);
CREATE INDEX idx_document_folders_level ON document_folders(level);
CREATE INDEX idx_document_folders_active ON document_folders(is_active, tenant_id);

-- OpenAI Vector Stores Indexes
CREATE INDEX idx_openai_vector_stores_tenant_id ON openai_vector_stores(tenant_id);
CREATE INDEX idx_openai_vector_stores_status ON openai_vector_stores(status);
CREATE INDEX idx_openai_vector_stores_expires_at ON openai_vector_stores(expires_at);

-- Documents Indexes
CREATE INDEX idx_documents_tenant_id ON documents(tenant_id);
CREATE INDEX idx_documents_folder_id ON documents(folder_id);
CREATE INDEX idx_documents_document_type ON documents(document_type, tenant_id);
CREATE INDEX idx_documents_created_by ON documents(created_by);
CREATE INDEX idx_documents_created_at ON documents(created_at);
CREATE INDEX idx_documents_active ON documents(is_active, tenant_id);
CREATE INDEX idx_documents_public ON documents(is_public, tenant_id);
CREATE INDEX idx_documents_processing_status ON documents(processing_status);
CREATE INDEX idx_documents_openai_file_id ON documents(openai_file_id);
CREATE INDEX idx_documents_openai_vector_store_id ON documents(openai_vector_store_id);
CREATE INDEX idx_documents_content_hash ON documents(content_hash);
CREATE INDEX idx_documents_tags ON documents USING GIN(tags);
CREATE INDEX idx_documents_metadata ON documents USING GIN(metadata);

-- Full-text search index for documents
CREATE INDEX idx_documents_search ON documents USING GIN(
    to_tsvector('english', COALESCE(title, '') || ' ' || COALESCE(description, '') || ' ' || COALESCE(extracted_text, ''))
);

-- Document Permissions Indexes
CREATE INDEX idx_document_permissions_tenant_id ON document_permissions(tenant_id);
CREATE INDEX idx_document_permissions_document_id ON document_permissions(document_id);
CREATE INDEX idx_document_permissions_user_id ON document_permissions(user_id);
CREATE INDEX idx_document_permissions_role_id ON document_permissions(role_id);
CREATE INDEX idx_document_permissions_department_id ON document_permissions(department_id);
CREATE INDEX idx_document_permissions_level ON document_permissions(permission_level);
CREATE INDEX idx_document_permissions_expires_at ON document_permissions(expires_at);
CREATE INDEX idx_document_permissions_inherited ON document_permissions(is_inherited);

-- Document Access Logs Indexes
CREATE INDEX idx_document_access_logs_tenant_id ON document_access_logs(tenant_id);
CREATE INDEX idx_document_access_logs_document_id ON document_access_logs(document_id);
CREATE INDEX idx_document_access_logs_user_id ON document_access_logs(user_id);
CREATE INDEX idx_document_access_logs_access_type ON document_access_logs(access_type);
CREATE INDEX idx_document_access_logs_created_at ON document_access_logs(created_at);
CREATE INDEX idx_document_access_logs_success ON document_access_logs(success);
CREATE INDEX idx_document_access_logs_session_id ON document_access_logs(session_id);



-- =====================================================
-- TABLE COMMENTS
-- =====================================================

-- Document Folders
COMMENT ON TABLE document_folders IS 'Hierarchical folder structure for organizing documents';
COMMENT ON COLUMN document_folders.path IS 'Auto-calculated hierarchical path (e.g., /parent/child)';
COMMENT ON COLUMN document_folders.level IS 'Depth level in hierarchy (0 = root level)';
COMMENT ON COLUMN document_folders.tenant_id IS 'Tenant ID for multi-tenancy isolation';

-- OpenAI Vector Stores
COMMENT ON TABLE openai_vector_stores IS 'Track OpenAI Vector Stores per tenant for RAG functionality';
COMMENT ON COLUMN openai_vector_stores.vector_store_id IS 'OpenAI Vector Store ID from OpenAI API';
COMMENT ON COLUMN openai_vector_stores.file_count IS 'Number of files in the vector store';
COMMENT ON COLUMN openai_vector_stores.usage_bytes IS 'Total storage usage in bytes';
COMMENT ON COLUMN openai_vector_stores.expires_at IS 'OpenAI vector store expiration timestamp';

-- Documents
COMMENT ON TABLE documents IS 'Main documents table with OpenAI integration and S3 storage';
COMMENT ON COLUMN documents.s3_key IS 'Unique S3 object key for file storage';
COMMENT ON COLUMN documents.openai_file_id IS 'OpenAI Files API ID for RAG integration';
COMMENT ON COLUMN documents.processing_status IS 'OpenAI processing status (pending, uploading_to_openai, adding_to_vector_store, completed, failed, retry)';
COMMENT ON COLUMN documents.content_hash IS 'SHA-256 hash for duplicate detection';
COMMENT ON COLUMN documents.extracted_text IS 'Extracted text content for full-text search';
COMMENT ON COLUMN documents.is_public IS 'Public documents bypass permission checks';
COMMENT ON COLUMN documents.tenant_id IS 'Tenant ID for multi-tenancy isolation';

-- Document Permissions
COMMENT ON TABLE document_permissions IS 'Multi-level permissions (user, role, department) for documents';
COMMENT ON COLUMN document_permissions.permission_level IS 'Permission level: read, write, admin';
COMMENT ON COLUMN document_permissions.is_inherited IS 'Whether permission is inherited from folder';
COMMENT ON COLUMN document_permissions.expires_at IS 'Optional permission expiration timestamp';

-- Document Access Logs
COMMENT ON TABLE document_access_logs IS 'Audit log for document access and RAG queries';
COMMENT ON COLUMN document_access_logs.access_type IS 'Type of access: view, download, search, rag_query, edit, delete';
COMMENT ON COLUMN document_access_logs.query_text IS 'Original query text for RAG searches';
COMMENT ON COLUMN document_access_logs.query_results IS 'RAG search results in JSON format';
COMMENT ON COLUMN document_access_logs.query_confidence IS 'AI confidence score (0.00-1.00)';



-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================
-- This migration creates the complete database schema for the document management module
-- with OpenAI integration, tenant isolation, and performance optimizations.
--
-- Tables created:
-- 1. document_folders - Hierarchical folder structure
-- 2. openai_vector_stores - OpenAI Vector Store management
-- 3. documents - Main documents with OpenAI integration
-- 4. document_permissions - Multi-level permissions
-- 5. document_access_logs - Audit logging and RAG queries
--
-- Features:
-- - Complete tenant isolation with tenantId fields
-- - OpenAI Files API and Vector Store integration
-- - S3 storage integration (direct upload from frontend)
-- - Multi-level permissions (user/role/department)
-- - Full-text search capabilities
-- - Comprehensive audit logging
-- - Performance-optimized indexes
-- =====================================================
