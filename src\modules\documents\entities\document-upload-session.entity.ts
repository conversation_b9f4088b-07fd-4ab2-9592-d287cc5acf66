import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '@/modules/auth/entities/user.entity';
import { DocumentFolder } from './document-folder.entity';
import { UploadStatus } from '../enums';

@Entity('document_upload_sessions')
@Index(['tenantId'])
@Index(['createdBy'])
@Index(['status'])
@Index(['expiresAt'])
@Index(['createdAt'])
@Index(['s3Key'])
@Index(['sessionId'], { unique: true })
@Index(['s3Key'], { unique: true })
export class DocumentUploadSession {
  /**
   * Primary key
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Unique session identifier (UUID)
   */
  @Column({ name: 'session_id', type: 'uuid', unique: true })
  sessionId: string;

  /**
   * Original file name
   */
  @Column({ name: 'file_name', type: 'varchar', length: 255 })
  fileName: string;

  /**
   * File size in bytes
   */
  @Column({ name: 'file_size', type: 'bigint' })
  fileSize: number;

  /**
   * MIME type
   */
  @Column({ name: 'mime_type', type: 'varchar', length: 100 })
  mimeType: string;

  /**
   * S3 bucket name
   */
  @Column({ name: 's3_bucket', type: 'varchar', length: 100 })
  s3Bucket: string;

  /**
   * S3 object key
   */
  @Column({ name: 's3_key', type: 'varchar', length: 500 })
  s3Key: string;

  /**
   * S3 presigned URL for direct upload
   */
  @Column({ name: 'presigned_url', type: 'text' })
  presignedUrl: string;

  /**
   * Upload status
   */
  @Column({ name: 'status', type: 'varchar', length: 50, default: UploadStatus.PENDING })
  status: UploadStatus;

  /**
   * Upload progress percentage
   */
  @Column({ name: 'upload_progress', type: 'decimal', precision: 5, scale: 2, default: 0.00 })
  uploadProgress: number;

  /**
   * Document title for creation after upload
   */
  @Column({ name: 'document_title', type: 'varchar', length: 500, nullable: true })
  documentTitle: string | null;

  /**
   * Document description for creation after upload
   */
  @Column({ name: 'document_description', type: 'text', nullable: true })
  documentDescription: string | null;

  /**
   * Document type for creation after upload
   */
  @Column({ name: 'document_type', type: 'varchar', length: 50, nullable: true })
  documentType: string | null;

  /**
   * Folder ID for document organization
   */
  @Column({ name: 'folder_id', type: 'integer', nullable: true })
  folderId: number | null;

  /**
   * Document tags
   */
  @Column({ name: 'tags', type: 'text', array: true, nullable: true })
  tags: string[] | null;

  /**
   * Additional metadata for document creation
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any> | null;

  /**
   * Session expiration timestamp
   */
  @Column({ name: 'expires_at', type: 'bigint' })
  expiresAt: number;

  /**
   * When file was uploaded to S3
   */
  @Column({ name: 'uploaded_at', type: 'bigint', nullable: true })
  uploadedAt: number | null;

  /**
   * When document was created
   */
  @Column({ name: 'completed_at', type: 'bigint', nullable: true })
  completedAt: number | null;

  /**
   * Error message if upload/processing failed
   */
  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string | null;

  /**
   * Number of retry attempts
   */
  @Column({ name: 'retry_count', type: 'integer', default: 0 })
  retryCount: number;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * User who created this upload session
   */
  @Column({ name: 'created_by', type: 'integer' })
  createdBy: number;

  /**
   * Tenant ID for multi-tenancy isolation
   */
  @Column({ name: 'tenant_id', type: 'integer' })
  tenantId: number;

  // =====================================================
  // RELATIONSHIPS
  // =====================================================

  /**
   * Folder relationship
   */
  @ManyToOne(() => DocumentFolder, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'folder_id' })
  folder: DocumentFolder | null;

  /**
   * User who created this upload session
   */
  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  // =====================================================
  // COMPUTED PROPERTIES
  // =====================================================

  /**
   * Check if session is expired
   */
  get isExpired(): boolean {
    return Date.now() > this.expiresAt;
  }

  /**
   * Check if upload is completed
   */
  get isCompleted(): boolean {
    return this.status === UploadStatus.COMPLETED;
  }

  /**
   * Check if upload failed
   */
  get isFailed(): boolean {
    return this.status === UploadStatus.FAILED;
  }

  /**
   * Check if upload can be retried
   */
  get canRetry(): boolean {
    return this.isFailed && this.retryCount < 3 && !this.isExpired;
  }

  /**
   * Get file size in MB
   */
  get fileSizeMB(): number {
    return Math.round(this.fileSize / (1024 * 1024) * 100) / 100;
  }

  /**
   * Get time until expiration in minutes
   */
  get minutesUntilExpiration(): number {
    const msUntilExpiration = this.expiresAt - Date.now();
    return Math.ceil(msUntilExpiration / (1000 * 60));
  }

  /**
   * Get file extension
   */
  get fileExtension(): string {
    return this.fileName.split('.').pop()?.toLowerCase() || '';
  }

  /**
   * Check if session is in a final state
   */
  get isFinalState(): boolean {
    return [
      UploadStatus.COMPLETED,
      UploadStatus.FAILED,
      UploadStatus.CANCELLED,
      UploadStatus.EXPIRED,
    ].includes(this.status);
  }
}
