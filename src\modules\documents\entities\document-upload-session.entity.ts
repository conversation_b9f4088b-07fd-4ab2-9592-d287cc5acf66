import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
} from 'typeorm';
import { UploadStatus } from '../enums';

@Entity('document_upload_sessions')
@Index(['tenantId'])
@Index(['createdBy'])
@Index(['status'])
@Index(['expiresAt'])
@Index(['createdAt'])
@Index(['s3Key'])
@Index(['sessionId'], { unique: true })
@Index(['s3Key'], { unique: true })
export class DocumentUploadSession {
  /**
   * Khóa chính
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Mã định danh phiên duy nhất (UUID)
   */
  @Column({ name: 'session_id', type: 'uuid', unique: true })
  sessionId: string;

  /**
   * Tên file gốc
   */
  @Column({ name: 'file_name', type: 'varchar', length: 255 })
  fileName: string;

  /**
   * <PERSON><PERSON>ch thước file tính bằng bytes
   */
  @Column({ name: 'file_size', type: 'bigint' })
  fileSize: number;

  /**
   * Loại MIME
   */
  @Column({ name: 'mime_type', type: 'varchar', length: 100 })
  mimeType: string;

  /**
   * Tên bucket S3
   */
  @Column({ name: 's3_bucket', type: 'varchar', length: 100 })
  s3Bucket: string;

  /**
   * Khóa object S3
   */
  @Column({ name: 's3_key', type: 'varchar', length: 500 })
  s3Key: string;

  /**
   * URL presigned S3 để upload trực tiếp
   */
  @Column({ name: 'presigned_url', type: 'text' })
  presignedUrl: string;

  /**
   * Trạng thái upload
   */
  @Column({ name: 'status', type: 'varchar', length: 50, default: UploadStatus.PENDING })
  status: UploadStatus;

  /**
   * Phần trăm tiến độ upload
   */
  @Column({ name: 'upload_progress', type: 'decimal', precision: 5, scale: 2, default: 0.00 })
  uploadProgress: number;

  /**
   * Tiêu đề tài liệu để tạo sau khi upload
   */
  @Column({ name: 'document_title', type: 'varchar', length: 500, nullable: true })
  documentTitle: string | null;

  /**
   * Mô tả tài liệu để tạo sau khi upload
   */
  @Column({ name: 'document_description', type: 'text', nullable: true })
  documentDescription: string | null;

  /**
   * Loại tài liệu để tạo sau khi upload
   */
  @Column({ name: 'document_type', type: 'varchar', length: 50, nullable: true })
  documentType: string | null;

  /**
   * ID thư mục để tổ chức tài liệu
   */
  @Column({ name: 'folder_id', type: 'integer', nullable: true })
  folderId: number | null;

  /**
   * Thẻ tag của tài liệu
   */
  @Column({ name: 'tags', type: 'text', array: true, nullable: true })
  tags: string[] | null;

  /**
   * Metadata bổ sung để tạo tài liệu
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any> | null;

  /**
   * Thời điểm hết hạn phiên
   */
  @Column({ name: 'expires_at', type: 'bigint' })
  expiresAt: number;

  /**
   * Thời điểm file được upload lên S3
   */
  @Column({ name: 'uploaded_at', type: 'bigint', nullable: true })
  uploadedAt: number | null;

  /**
   * Thời điểm tài liệu được tạo
   */
  @Column({ name: 'completed_at', type: 'bigint', nullable: true })
  completedAt: number | null;

  /**
   * Thông báo lỗi nếu upload/xử lý thất bại
   */
  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string | null;

  /**
   * Số lần thử lại
   */
  @Column({ name: 'retry_count', type: 'integer', default: 0 })
  retryCount: number;

  /**
   * Thời điểm tạo (timestamp tính bằng milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối (timestamp tính bằng milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID người dùng tạo phiên upload này
   */
  @Column({ name: 'created_by', type: 'integer' })
  createdBy: number;

  /**
   * ID tenant để phân tách dữ liệu multi-tenant
   */
  @Column({ name: 'tenant_id', type: 'integer' })
  tenantId: number;

  // =====================================================
  // THUỘC TÍNH TÍNH TOÁN
  // =====================================================

  /**
   * Kiểm tra phiên đã hết hạn chưa
   */
  get isExpired(): boolean {
    return Date.now() > this.expiresAt;
  }

  /**
   * Kiểm tra upload đã hoàn thành chưa
   */
  get isCompleted(): boolean {
    return this.status === UploadStatus.COMPLETED;
  }

  /**
   * Kiểm tra upload có thất bại không
   */
  get isFailed(): boolean {
    return this.status === UploadStatus.FAILED;
  }

  /**
   * Kiểm tra upload có thể thử lại không
   */
  get canRetry(): boolean {
    return this.isFailed && this.retryCount < 3 && !this.isExpired;
  }

  /**
   * Lấy kích thước file tính bằng MB
   */
  get fileSizeMB(): number {
    return Math.round(this.fileSize / (1024 * 1024) * 100) / 100;
  }

  /**
   * Lấy thời gian còn lại đến khi hết hạn tính bằng phút
   */
  get minutesUntilExpiration(): number {
    const msUntilExpiration = this.expiresAt - Date.now();
    return Math.ceil(msUntilExpiration / (1000 * 60));
  }

  /**
   * Lấy phần mở rộng file
   */
  get fileExtension(): string {
    return this.fileName.split('.').pop()?.toLowerCase() || '';
  }

  /**
   * Kiểm tra phiên có ở trạng thái cuối không
   */
  get isFinalState(): boolean {
    return [
      UploadStatus.COMPLETED,
      UploadStatus.FAILED,
      UploadStatus.CANCELLED,
      UploadStatus.EXPIRED,
    ].includes(this.status);
  }
}
