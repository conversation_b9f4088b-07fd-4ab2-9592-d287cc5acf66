import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
} from 'typeorm';

@Entity('openai_vector_stores')
@Index(['tenantId'], { unique: true })
@Index(['vectorStoreId'], { unique: true })
@Index(['status'])
@Index(['expiresAt'])
export class OpenAIVectorStore {
  /**
   * Primary key
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * OpenAI Vector Store ID from OpenAI API
   */
  @Column({ name: 'vector_store_id', type: 'varchar', length: 100, unique: true })
  vectorStoreId: string;

  /**
   * Vector store name
   */
  @Column({ name: 'name', type: 'varchar', length: 255 })
  name: string;

  /**
   * Vector store description
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * Number of files in the vector store
   */
  @Column({ name: 'file_count', type: 'integer', default: 0 })
  fileCount: number;

  /**
   * Total storage usage in bytes
   */
  @Column({ name: 'usage_bytes', type: 'bigint', default: 0 })
  usageBytes: number;

  /**
   * Vector store status
   */
  @Column({ name: 'status', type: 'varchar', length: 50, default: 'active' })
  status: 'active' | 'expired' | 'deleting';

  /**
   * OpenAI vector store expiration timestamp
   */
  @Column({ name: 'expires_at', type: 'bigint', nullable: true })
  expiresAt: number | null;

  /**
   * Last sync with OpenAI timestamp
   */
  @Column({ name: 'last_synced_at', type: 'bigint', nullable: true })
  lastSyncedAt: number | null;

  /**
   * OpenAI metadata
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any> | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * Tenant ID for multi-tenancy isolation
   */
  @Column({ name: 'tenant_id', type: 'integer' })
  tenantId: number;

  // =====================================================
  // COMPUTED PROPERTIES
  // =====================================================

  /**
   * Check if the vector store is expired
   */
  get isExpired(): boolean {
    if (!this.expiresAt) return false;
    return Date.now() > this.expiresAt;
  }

  /**
   * Check if the vector store is active and not expired
   */
  get isActive(): boolean {
    return this.status === 'active' && !this.isExpired;
  }

  /**
   * Get usage in MB
   */
  get usageMB(): number {
    return Math.round(this.usageBytes / (1024 * 1024) * 100) / 100;
  }

  /**
   * Get days until expiration
   */
  get daysUntilExpiration(): number | null {
    if (!this.expiresAt) return null;
    const msUntilExpiration = this.expiresAt - Date.now();
    return Math.ceil(msUntilExpiration / (1000 * 60 * 60 * 24));
  }
}
