import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  JoinColumn,
  Index,
  Check,
} from 'typeorm';
import { User } from '@/modules/auth/entities/user.entity';
import { Document } from './document.entity';
import { PermissionLevel } from '../enums';

@Entity('document_permissions')
@Index(['tenantId'])
@Index(['documentId'])
@Index(['userId'])
@Index(['roleId'])
@Index(['departmentId'])
@Index(['permissionLevel'])
@Index(['expiresAt'])
@Index(['isInherited'])
@Index(['documentId', 'userId', 'tenantId'], { unique: true })
@Index(['documentId', 'roleId', 'tenantId'], { unique: true })
@Index(['documentId', 'departmentId', 'tenantId'], { unique: true })
@Check(`(
  (user_id IS NOT NULL AND role_id IS NULL AND department_id IS NULL) OR
  (user_id IS NULL AND role_id IS NOT NULL AND department_id IS NULL) OR
  (user_id IS NULL AND role_id IS NULL AND department_id IS NOT NULL)
)`)
export class DocumentPermission {
  /**
   * Primary key
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Document ID
   */
  @Column({ name: 'document_id', type: 'integer' })
  documentId: number;

  /**
   * User ID for user-specific permission
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * Role ID for role-based permission
   */
  @Column({ name: 'role_id', type: 'integer', nullable: true })
  roleId: number | null;

  /**
   * Department ID for department-based permission
   */
  @Column({ name: 'department_id', type: 'integer', nullable: true })
  departmentId: number | null;

  /**
   * Permission level
   */
  @Column({ name: 'permission_level', type: 'varchar', length: 20 })
  permissionLevel: PermissionLevel;

  /**
   * User who granted this permission
   */
  @Column({ name: 'granted_by', type: 'integer' })
  grantedBy: number;

  /**
   * When the permission was granted
   */
  @Column({ name: 'granted_at', type: 'bigint' })
  grantedAt: number;

  /**
   * Optional permission expiration timestamp
   */
  @Column({ name: 'expires_at', type: 'bigint', nullable: true })
  expiresAt: number | null;

  /**
   * Whether permission is inherited from folder
   */
  @Column({ name: 'is_inherited', type: 'boolean', default: false })
  isInherited: boolean;

  /**
   * Optional notes about permission
   */
  @Column({ name: 'notes', type: 'text', nullable: true })
  notes: string | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * Tenant ID for multi-tenancy isolation
   */
  @Column({ name: 'tenant_id', type: 'integer' })
  tenantId: number;

  // =====================================================
  // RELATIONSHIPS
  // =====================================================

  /**
   * Document relationship
   */
  @ManyToOne(() => Document, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'document_id' })
  document: Document;

  /**
   * User relationship (for user-specific permissions)
   */
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User | null;

  /**
   * User who granted this permission
   */
  @ManyToOne(() => User)
  @JoinColumn({ name: 'granted_by' })
  grantor: User;

  // =====================================================
  // COMPUTED PROPERTIES
  // =====================================================

  /**
   * Check if permission is expired
   */
  get isExpired(): boolean {
    if (!this.expiresAt) return false;
    return Date.now() > this.expiresAt;
  }

  /**
   * Check if permission is currently valid
   */
  get isValid(): boolean {
    return !this.isExpired;
  }

  /**
   * Get permission target type
   */
  get targetType(): 'user' | 'role' | 'department' {
    if (this.userId) return 'user';
    if (this.roleId) return 'role';
    if (this.departmentId) return 'department';
    throw new Error('Invalid permission target');
  }

  /**
   * Get permission target ID
   */
  get targetId(): number {
    if (this.userId) return this.userId;
    if (this.roleId) return this.roleId;
    if (this.departmentId) return this.departmentId;
    throw new Error('Invalid permission target');
  }

  /**
   * Get days until expiration
   */
  get daysUntilExpiration(): number | null {
    if (!this.expiresAt) return null;
    const msUntilExpiration = this.expiresAt - Date.now();
    return Math.ceil(msUntilExpiration / (1000 * 60 * 60 * 24));
  }
}
