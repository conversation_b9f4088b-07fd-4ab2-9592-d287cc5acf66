import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '@/modules/auth/entities/user.entity';
import { Document } from './document.entity';

export type AccessType = 'view' | 'download' | 'search' | 'rag_query' | 'edit' | 'delete';

@Entity('document_access_logs')
@Index(['tenantId'])
@Index(['documentId'])
@Index(['userId'])
@Index(['accessType'])
@Index(['createdAt'])
@Index(['success'])
@Index(['sessionId'])
export class DocumentAccessLog {
  /**
   * Primary key
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Document ID (nullable for general searches)
   */
  @Column({ name: 'document_id', type: 'integer', nullable: true })
  documentId: number | null;

  /**
   * User ID who performed the action
   */
  @Column({ name: 'user_id', type: 'integer' })
  userId: number;

  /**
   * Type of access
   */
  @Column({ name: 'access_type', type: 'varchar', length: 50 })
  accessType: AccessType;

  /**
   * Original query text for RAG searches
   */
  @Column({ name: 'query_text', type: 'text', nullable: true })
  queryText: string | null;

  /**
   * RAG search results in JSON format
   */
  @Column({ name: 'query_results', type: 'jsonb', nullable: true })
  queryResults: Record<string, any> | null;

  /**
   * AI confidence score (0.00-1.00)
   */
  @Column({ name: 'query_confidence', type: 'decimal', precision: 3, scale: 2, nullable: true })
  queryConfidence: number | null;

  /**
   * Client IP address
   */
  @Column({ name: 'ip_address', type: 'inet', nullable: true })
  ipAddress: string | null;

  /**
   * User agent string
   */
  @Column({ name: 'user_agent', type: 'text', nullable: true })
  userAgent: string | null;

  /**
   * Session ID
   */
  @Column({ name: 'session_id', type: 'varchar', length: 100, nullable: true })
  sessionId: string | null;

  /**
   * Response time in milliseconds
   */
  @Column({ name: 'response_time_ms', type: 'integer', nullable: true })
  responseTimeMs: number | null;

  /**
   * Whether the operation was successful
   */
  @Column({ name: 'success', type: 'boolean', default: true })
  success: boolean;

  /**
   * Error message if operation failed
   */
  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string | null;

  /**
   * Additional metadata
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any> | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Tenant ID for multi-tenancy isolation
   */
  @Column({ name: 'tenant_id', type: 'integer' })
  tenantId: number;

  // =====================================================
  // RELATIONSHIPS
  // =====================================================

  /**
   * Document relationship (nullable for general searches)
   */
  @ManyToOne(() => Document, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'document_id' })
  document: Document | null;

  /**
   * User who performed the action
   */
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  // =====================================================
  // COMPUTED PROPERTIES
  // =====================================================

  /**
   * Check if this is a RAG query log
   */
  get isRagQuery(): boolean {
    return this.accessType === 'rag_query';
  }

  /**
   * Check if this is a search log
   */
  get isSearch(): boolean {
    return this.accessType === 'search' || this.accessType === 'rag_query';
  }

  /**
   * Check if this is a document access log
   */
  get isDocumentAccess(): boolean {
    return this.documentId !== null;
  }

  /**
   * Get response time in seconds
   */
  get responseTimeSeconds(): number | null {
    if (!this.responseTimeMs) return null;
    return Math.round(this.responseTimeMs / 1000 * 100) / 100;
  }

  /**
   * Get confidence percentage
   */
  get confidencePercentage(): number | null {
    if (!this.queryConfidence) return null;
    return Math.round(this.queryConfidence * 100);
  }

  /**
   * Get formatted date
   */
  get formattedDate(): string {
    return new Date(this.createdAt).toISOString();
  }

  /**
   * Check if the operation was slow (> 3 seconds)
   */
  get isSlow(): boolean {
    if (!this.responseTimeMs) return false;
    return this.responseTimeMs > 3000;
  }
}
